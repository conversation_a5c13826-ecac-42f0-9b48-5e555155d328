"""
Common test classes for ERP testing framework
Provides Odoo-like test base classes with transaction management
"""
import unittest
import asyncio
import sys
import os
from typing import Dict, Any, Optional, Type, Union
from unittest.mock import Mock, patch

# Add project root to path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

from erp.config import config
from erp.models.async_base import AsyncBaseModel, AsyncModelRegistry
from erp.addons.async_loader import AsyncAddonLoader
from erp.database.async_registry import AsyncDatabaseRegistry


class TestCase(unittest.TestCase):
    """Base test case for ERP tests"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class"""
        super().setUpClass()
        cls._setup_test_environment()
    
    @classmethod
    def tearDownClass(cls):
        """Tear down test class"""
        cls._cleanup_test_environment()
        super().tearDownClass()
    
    @classmethod
    def _setup_test_environment(cls):
        """Setup test environment"""
        # Save original state
        cls._original_models = ModelRegistry._models.copy()
        if ASYNC_AVAILABLE:
            cls._original_async_models = AsyncModelRegistry._models.copy()
        
        # Setup test configuration
        cls._setup_test_config()
    
    @classmethod
    def _cleanup_test_environment(cls):
        """Cleanup test environment"""
        # Restore original state
        ModelRegistry._models = cls._original_models
        if ASYNC_AVAILABLE:
            AsyncModelRegistry._models = cls._original_async_models
    
    @classmethod
    def _setup_test_config(cls):
        """Setup test configuration"""
        # Override config for testing
        config.set('options', 'db_name', 'erp_test')
        config.set('options', 'addons_path', 'addons')
    
    def setUp(self):
        """Set up test case"""
        super().setUp()
        self.env = self._create_test_environment()
    
    def tearDown(self):
        """Tear down test case"""
        self._cleanup_test_case()
        super().tearDown()
    
    def _create_test_environment(self):
        """Create test environment similar to Odoo's env"""
        return TestEnvironment()
    
    def _cleanup_test_case(self):
        """Cleanup test case"""
        pass


class TransactionCase(TestCase):
    """Test case with transaction rollback after each test"""
    
    def setUp(self):
        """Set up test with transaction"""
        super().setUp()
        self._start_transaction()
    
    def tearDown(self):
        """Tear down test with transaction rollback"""
        self._rollback_transaction()
        super().tearDown()
    
    def _start_transaction(self):
        """Start database transaction"""
        # Mock transaction for now - implement with actual DB later
        self._transaction_started = True
    
    def _rollback_transaction(self):
        """Rollback database transaction"""
        # Mock rollback for now - implement with actual DB later
        self._transaction_started = False


class SingleTransactionCase(TestCase):
    """Test case with single transaction for entire test class"""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class with transaction"""
        super().setUpClass()
        cls._start_class_transaction()
    
    @classmethod
    def tearDownClass(cls):
        """Tear down test class with transaction rollback"""
        cls._rollback_class_transaction()
        super().tearDownClass()
    
    @classmethod
    def _start_class_transaction(cls):
        """Start database transaction for entire class"""
        # Mock transaction for now - implement with actual DB later
        cls._class_transaction_started = True
    
    @classmethod
    def _rollback_class_transaction(cls):
        """Rollback database transaction for entire class"""
        # Mock rollback for now - implement with actual DB later
        cls._class_transaction_started = False


class AsyncTestCase(TestCase):
    """Base async test case for ERP tests"""
    
    def setUp(self):
        """Set up async test case"""
        super().setUp()
        if not ASYNC_AVAILABLE:
            self.skipTest("Async functionality not available")
        
        # Create event loop for test
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def tearDown(self):
        """Tear down async test case"""
        if hasattr(self, 'loop'):
            self.loop.close()
        super().tearDown()
    
    def run_async(self, coro):
        """Run async coroutine in test"""
        return self.loop.run_until_complete(coro)


class TestEnvironment:
    """Test environment similar to Odoo's env"""
    
    def __init__(self):
        self._models = {}
        self._context = {}
        self._user_id = 1
    
    def __getitem__(self, model_name: str):
        """Get model by name"""
        if model_name not in self._models:
            model_class = ModelRegistry.get(model_name)
            if model_class:
                self._models[model_name] = model_class
            else:
                raise KeyError(f"Model {model_name} not found")
        return self._models[model_name]
    
    def ref(self, xml_id: str):
        """Get record by XML ID (mock implementation)"""
        # Mock implementation - would normally look up XML ID
        return Mock(id=1, name=xml_id)
    
    def with_context(self, **context):
        """Create new environment with updated context"""
        new_env = TestEnvironment()
        new_env._models = self._models.copy()
        new_env._context = {**self._context, **context}
        new_env._user_id = self._user_id
        return new_env
    
    def with_user(self, user_id: int):
        """Create new environment with different user"""
        new_env = TestEnvironment()
        new_env._models = self._models.copy()
        new_env._context = self._context.copy()
        new_env._user_id = user_id
        return new_env
    
    @property
    def context(self):
        """Get current context"""
        return self._context.copy()
    
    @property
    def user(self):
        """Get current user (mock)"""
        return Mock(id=self._user_id, name=f"User {self._user_id}")
