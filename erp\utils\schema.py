"""
Schema generation and comparison utilities for ERP models
"""
import asyncio
from typing import Dict, List, Optional, Any, Tuple
from ..models.async_base import AsyncModelRegistry
from ..database.async_registry import AsyncDatabaseRegistry


class SchemaGenerator:
    """Utility for generating database schema from model definitions"""
    
    @classmethod
    def get_model_schema(cls, model_name: str) -> Optional[Dict[str, Any]]:
        """
        Generate schema for a model given its name

        Args:
            model_name: Technical name of the model (e.g., 'ir.model')

        Returns:
            Dictionary containing schema information or None if model not found
        """
        # Get model class from registry
        model_class = AsyncModelRegistry.get(model_name)

        if not model_class:
            return None
            
        # Extract schema information
        schema = {
            'model_name': model_name,
            'table_name': model_class._table or model_name.replace('.', '_'),
            'description': model_class._description,
            'fields': {},
            'indexes': [],
            'constraints': []
        }
        
        # Process fields
        for field_name, field in model_class._fields.items():
            field_info = {
                'name': field_name,
                'type': field.__class__.__name__.lower(),
                'sql_type': field.get_sql_type(),
                'required': field.required,
                'readonly': field.readonly,
                'string': field.string,
                'help': field.help,
                'default': field.get_default_value() if hasattr(field, 'get_default_value') else None
            }
            
            # Add type-specific information
            if hasattr(field, 'size') and field.size:
                field_info['size'] = field.size
            if hasattr(field, 'selection') and field.selection:
                field_info['selection'] = field.selection
            if hasattr(field, 'comodel_name') and field.comodel_name:
                field_info['comodel_name'] = field.comodel_name
            if hasattr(field, 'inverse_name') and field.inverse_name:
                field_info['inverse_name'] = field.inverse_name
                
            schema['fields'][field_name] = field_info
            
            # Add index information
            if field.index:
                schema['indexes'].append({
                    'name': f"idx_{schema['table_name']}_{field_name}",
                    'columns': [field_name],
                    'unique': False
                })
        
        # Add primary key constraint
        schema['constraints'].append({
            'name': f"pk_{schema['table_name']}",
            'type': 'PRIMARY KEY',
            'columns': ['id']
        })
        
        return schema
    
    @classmethod
    def generate_create_table_sql(cls, model_name: str, use_async: bool = True) -> Optional[str]:
        """
        Generate CREATE TABLE SQL statement for a model
        
        Args:
            model_name: Technical name of the model
            use_async: Whether to use async model registry
            
        Returns:
            SQL CREATE TABLE statement or None if model not found
        """
        schema = cls.get_model_schema(model_name, use_async)
        if not schema:
            return None
            
        table_name = schema['table_name']
        columns = []
        
        # Add field columns
        for field_name, field_info in schema['fields'].items():
            sql_type = field_info['sql_type']
            if not sql_type:  # Skip fields without SQL representation (like One2many)
                continue
                
            column_def = f"{field_name} {sql_type}"
            
            if field_info['required']:
                column_def += " NOT NULL"
                
            if field_info['default'] is not None:
                default_val = field_info['default']
                if isinstance(default_val, str):
                    column_def += f" DEFAULT '{default_val}'"
                elif isinstance(default_val, bool):
                    column_def += f" DEFAULT {str(default_val).upper()}"
                else:
                    column_def += f" DEFAULT {default_val}"
                    
            columns.append(column_def)
        
        # Add primary key
        columns.append("PRIMARY KEY (id)")
        
        sql = f"""CREATE TABLE IF NOT EXISTS {table_name} (
    {',\n    '.join(columns)}
);"""
        
        # Add indexes
        index_sqls = []
        for index in schema['indexes']:
            index_sql = f"CREATE INDEX IF NOT EXISTS {index['name']} ON {table_name} ({', '.join(index['columns'])});"
            index_sqls.append(index_sql)
            
        if index_sqls:
            sql += "\n\n" + "\n".join(index_sqls)
            
        return sql
    
    @classmethod
    def get_all_models_schema(cls) -> Dict[str, Dict[str, Any]]:
        """
        Get schema for all registered models

        Returns:
            Dictionary mapping model names to their schemas
        """
        models = AsyncModelRegistry.all()

        schemas = {}
        for model_name in models.keys():
            schema = cls.get_model_schema(model_name)
            if schema:
                schemas[model_name] = schema

        return schemas


class SchemaComparator:
    """Utility for comparing model schemas with database tables"""
    
    @classmethod
    async def get_table_schema(cls, table_name: str) -> Optional[Dict[str, Any]]:
        """
        Get actual table schema from database
        
        Args:
            table_name: Name of the database table
            
        Returns:
            Dictionary containing actual table schema or None if table doesn't exist
        """
        if not AsyncDatabaseRegistry:
            raise RuntimeError("Async database not available")

        db = await AsyncDatabaseRegistry.get_current_database()
        if not db:
            raise RuntimeError("No database connection available")
        
        # Check if table exists
        exists_query = """
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = $1
            )
        """
        result = await db.execute(exists_query, (table_name,))
        if not result or not result[0]['exists']:
            return None
        
        # Get column information
        columns_query = """
            SELECT 
                column_name,
                data_type,
                is_nullable,
                column_default,
                character_maximum_length
            FROM information_schema.columns
            WHERE table_schema = 'public' AND table_name = $1
            ORDER BY ordinal_position
        """
        columns = await db.execute(columns_query, (table_name,))
        
        # Get indexes
        indexes_query = """
            SELECT 
                indexname,
                indexdef
            FROM pg_indexes
            WHERE tablename = $1 AND schemaname = 'public'
        """
        indexes = await db.execute(indexes_query, (table_name,))
        
        # Get constraints
        constraints_query = """
            SELECT 
                conname,
                contype,
                pg_get_constraintdef(oid) as definition
            FROM pg_constraint
            WHERE conrelid = $1::regclass
        """
        constraints = await db.execute(constraints_query, (table_name,))
        
        schema = {
            'table_name': table_name,
            'columns': {col['column_name']: col for col in columns},
            'indexes': [idx['indexname'] for idx in indexes],
            'constraints': [{'name': c['conname'], 'type': c['contype'], 'definition': c['definition']} for c in constraints]
        }
        
        return schema
    
    @classmethod
    async def compare_model_with_table(cls, model_name: str) -> Dict[str, Any]:
        """
        Compare model definition with actual database table
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            Dictionary containing comparison results and differences
        """
        # Get model schema
        model_schema = SchemaGenerator.get_model_schema(model_name, use_async=True)
        if not model_schema:
            return {
                'status': 'error',
                'message': f"Model '{model_name}' not found in registry"
            }
        
        table_name = model_schema['table_name']
        
        # Get actual table schema
        table_schema = await cls.get_table_schema(table_name)
        if not table_schema:
            return {
                'status': 'missing_table',
                'message': f"Table '{table_name}' does not exist in database",
                'model_schema': model_schema,
                'suggested_sql': SchemaGenerator.generate_create_table_sql(model_name)
            }
        
        # Compare schemas
        differences = {
            'missing_columns': [],
            'extra_columns': [],
            'type_mismatches': [],
            'constraint_differences': []
        }
        
        # Check for missing and extra columns
        model_fields = set(field_name for field_name, field_info in model_schema['fields'].items() 
                          if field_info['sql_type'] is not None)
        table_columns = set(table_schema['columns'].keys())
        
        differences['missing_columns'] = list(model_fields - table_columns)
        differences['extra_columns'] = list(table_columns - model_fields)
        
        # Check for type mismatches (simplified comparison)
        for field_name in model_fields & table_columns:
            model_field = model_schema['fields'][field_name]
            table_column = table_schema['columns'][field_name]
            
            # Simplified type comparison - in real implementation, this would be more sophisticated
            model_type = model_field['sql_type'].upper()
            table_type = table_column['data_type'].upper()
            
            if not cls._types_compatible(model_type, table_type):
                differences['type_mismatches'].append({
                    'field': field_name,
                    'model_type': model_type,
                    'table_type': table_type
                })
        
        # Determine overall status
        has_differences = any(differences.values())
        status = 'differences_found' if has_differences else 'synchronized'
        
        return {
            'status': status,
            'model_name': model_name,
            'table_name': table_name,
            'differences': differences,
            'model_schema': model_schema,
            'table_schema': table_schema
        }
    
    @classmethod
    def _types_compatible(cls, model_type: str, table_type: str) -> bool:
        """
        Check if model field type is compatible with database column type
        
        Args:
            model_type: SQL type from model field
            table_type: Actual database column type
            
        Returns:
            True if types are compatible
        """
        # Normalize types for comparison
        model_type = model_type.upper()
        table_type = table_type.upper()
        
        # Define type compatibility mappings
        compatible_types = {
            'TEXT': ['TEXT', 'CHARACTER VARYING', 'VARCHAR'],
            'VARCHAR': ['TEXT', 'CHARACTER VARYING', 'VARCHAR'],
            'INTEGER': ['INTEGER', 'BIGINT', 'SMALLINT'],
            'REAL': ['REAL', 'DOUBLE PRECISION', 'NUMERIC'],
            'BOOLEAN': ['BOOLEAN'],
            'DATE': ['DATE'],
            'TIMESTAMP': ['TIMESTAMP', 'TIMESTAMP WITHOUT TIME ZONE', 'TIMESTAMP WITH TIME ZONE'],
        }
        
        # Extract base type (remove size specifications)
        model_base = model_type.split('(')[0].strip()
        table_base = table_type.split('(')[0].strip()
        
        # Check compatibility
        for base_type, compatible in compatible_types.items():
            if model_base == base_type:
                return table_base in compatible
                
        # Default: exact match required
        return model_base == table_base
    
    @classmethod
    async def generate_migration_sql(cls, model_name: str) -> Optional[str]:
        """
        Generate SQL to migrate database table to match model definition
        
        Args:
            model_name: Technical name of the model
            
        Returns:
            SQL statements to perform migration or None if no changes needed
        """
        comparison = await cls.compare_model_with_table(model_name)
        
        if comparison['status'] == 'error':
            return None
        elif comparison['status'] == 'missing_table':
            return comparison['suggested_sql']
        elif comparison['status'] == 'synchronized':
            return None
            
        # Generate ALTER TABLE statements for differences
        table_name = comparison['table_name']
        differences = comparison['differences']
        model_schema = comparison['model_schema']
        
        sql_statements = []
        
        # Add missing columns
        for field_name in differences['missing_columns']:
            field_info = model_schema['fields'][field_name]
            sql_type = field_info['sql_type']
            
            alter_sql = f"ALTER TABLE {table_name} ADD COLUMN {field_name} {sql_type}"
            if field_info['required']:
                alter_sql += " NOT NULL"
            if field_info['default'] is not None:
                default_val = field_info['default']
                if isinstance(default_val, str):
                    alter_sql += f" DEFAULT '{default_val}'"
                else:
                    alter_sql += f" DEFAULT {default_val}"
                    
            sql_statements.append(alter_sql + ";")
        
        # Note: Removing extra columns and changing types would require more careful handling
        # in a production system to avoid data loss
        
        return "\n".join(sql_statements) if sql_statements else None
